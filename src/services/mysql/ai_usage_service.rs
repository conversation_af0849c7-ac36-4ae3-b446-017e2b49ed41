use std::sync::Arc;
use log::{debug, error};
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::models::mysql::MySqlAiUsage;

/// AI使用次数服务错误
#[derive(Debug, Error)]
pub enum MySqlAiUsageServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("参数无效")]
    InvalidParameter,

    #[error("记录不存在")]
    RecordNotFound,

    #[error("使用次数不足")]
    InsufficientUsage,

    #[error("无权使用AI功能")]
    NoPermission,
}

/// MySQL AI使用次数服务
pub struct MySqlAiUsageService {
    /// MySQL连接池
    pool: Arc<Pool<MySql>>,
}

impl MySqlAiUsageService {
    /// 创建新的AI使用次数服务
    pub fn new(pool: Pool<MySql>) -> Self {
        Self {
            pool: Arc::new(pool),
        }
    }

    /// 创建AI使用记录并设置次数为20
    pub async fn create_usage_record(&self, user_id: u64) -> Result<MySqlAiUsage, MySqlAiUsageServiceError> {
        // 检查是否已存在记录
        if let Ok(_) = self.get_usage_by_user_id(user_id).await {
            return Err(MySqlAiUsageServiceError::InvalidParameter);
        }

        // 插入新记录，设置次数为20
        let record_id = sqlx::query(
            "INSERT INTO ai_usage (user_id, usage_count) VALUES (?, ?)"
        )
        .bind(user_id)
        .bind(20)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的记录
        let record = sqlx::query_as::<_, MySqlAiUsage>(
            "SELECT * FROM ai_usage WHERE id = ?"
        )
        .bind(record_id)
        .fetch_one(&*self.pool)
        .await?;

        debug!("创建AI使用记录成功，用户ID: {}, 记录ID: {}", user_id, record_id);
        Ok(record)
    }

    /// 获取用户的使用次数
    pub async fn get_usage_by_user_id(&self, user_id: u64) -> Result<MySqlAiUsage, MySqlAiUsageServiceError> {
        let record = sqlx::query_as::<_, MySqlAiUsage>(
            "SELECT * FROM ai_usage WHERE user_id = ?"
        )
        .bind(user_id)
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlAiUsageServiceError::RecordNotFound)?;

        Ok(record)
    }

    /// 检查用户是否有权限使用AI功能
    pub async fn check_permission(&self, user_id: u64) -> Result<(), MySqlAiUsageServiceError> {
        match self.get_usage_by_user_id(user_id).await {
            Ok(record) => {
                let usage_count = record.usage_count.unwrap_or(0);
                if usage_count <= 0 {
                    Err(MySqlAiUsageServiceError::InsufficientUsage)
                } else {
                    Ok(())
                }
            }
            Err(MySqlAiUsageServiceError::RecordNotFound) => {
                Err(MySqlAiUsageServiceError::NoPermission)
            }
            Err(e) => Err(e),
        }
    }

    /// 减少用户使用次数
    pub async fn decrease_usage(&self, user_id: u64) -> Result<MySqlAiUsage, MySqlAiUsageServiceError> {
        // 先检查权限
        self.check_permission(user_id).await?;

        // 减少使用次数
        let rows_affected = sqlx::query(
            "UPDATE ai_usage SET usage_count = usage_count - 1, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND usage_count > 0"
        )
        .bind(user_id)
        .execute(&*self.pool)
        .await?
        .rows_affected();

        if rows_affected == 0 {
            return Err(MySqlAiUsageServiceError::InsufficientUsage);
        }

        // 获取更新后的记录
        let record = self.get_usage_by_user_id(user_id).await?;
        debug!("减少用户使用次数成功，用户ID: {}, 剩余次数: {}", user_id, record.usage_count.unwrap_or(0));
        Ok(record)
    }

    /// 设置用户使用次数
    pub async fn set_usage_count(&self, user_id: u64, usage_count: i32) -> Result<MySqlAiUsage, MySqlAiUsageServiceError> {
        // 检查参数有效性
        if usage_count < 0 {
            return Err(MySqlAiUsageServiceError::InvalidParameter);
        }

        // 更新使用次数
        let rows_affected = sqlx::query(
            "UPDATE ai_usage SET usage_count = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?"
        )
        .bind(usage_count)
        .bind(user_id)
        .execute(&*self.pool)
        .await?
        .rows_affected();

        if rows_affected == 0 {
            return Err(MySqlAiUsageServiceError::RecordNotFound);
        }

        // 获取更新后的记录
        let record = self.get_usage_by_user_id(user_id).await?;
        debug!("设置用户使用次数成功，用户ID: {}, 设置次数: {}", user_id, usage_count);
        Ok(record)
    }
}
