use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{info, error};
use serde_json::Value;

use crate::dto::{ApiResponse, ErrorCode};
use crate::dto::ai_usage::{SetAiUsageRequest, SetAiUsageResponse};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::AppState;

/**
 * 设置用户AI使用次数处理函数
 *
 * 此函数处理POST /ai-usage/set请求，用于设置用户的AI使用次数
 *
 * 请求参数：
 * {
 *   "user_id": "用户ID",
 *   "usage_count": 使用次数
 * }
 *
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "设置成功",
 *   "data": {
 *     "id": "记录ID",
 *     "user_id": "用户ID",
 *     "usage_count": 使用次数
 *   }
 * }
 *
 * @param req HTTP请求
 * @param payload 请求体，包含用户ID和使用次数
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    payload: web::Json<SetAiUsageRequest>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID（管理员权限验证）
    let current_user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析目标用户ID
    let user_id = match payload.user_id.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式无效: {}", payload.user_id);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    // 验证使用次数参数
    if payload.usage_count < 0 {
        error!("使用次数不能为负数: {}", payload.usage_count);
        return HttpResponse::BadRequest().json(ApiResponse {
            code: ErrorCode::InvalidParameter.code(),
            message: "使用次数不能为负数".to_string(),
            data: Value::Null,
        });
    }

    info!("设置用户AI使用次数，用户ID: {}, 次数: {}", user_id, payload.usage_count);

    // 获取MySQL AI使用次数服务
    let mysql_ai_usage_service = match &app_state.mysql_ai_usage_service {
        Some(service) => service,
        None => {
            error!("MySQL AI使用次数服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 设置AI使用次数
    match mysql_ai_usage_service.set_usage_count(user_id, payload.usage_count).await {
        Ok(record) => {
            let record_id_str = record.id.to_string();
            let usage_count = record.usage_count.unwrap_or(0);
            info!("设置AI使用次数成功，用户ID: {}, 次数: {}", user_id, usage_count);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "设置成功".to_string(),
                data: SetAiUsageResponse {
                    id: record_id_str,
                    user_id: payload.user_id.clone(),
                    usage_count,
                },
            })
        }
        Err(e) => {
            error!("设置AI使用次数失败: {:?}", e);
            let (code, message) = match e {
                crate::services::mysql::MySqlAiUsageServiceError::RecordNotFound => {
                    (ErrorCode::DataNotFound.code(), "用户AI使用记录不存在".to_string())
                }
                crate::services::mysql::MySqlAiUsageServiceError::InvalidParameter => {
                    (ErrorCode::InvalidParameter.code(), "参数无效".to_string())
                }
                _ => (ErrorCode::DatabaseError.code(), "数据库错误".to_string()),
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code,
                message,
                data: Value::Null,
            })
        }
    }
}
