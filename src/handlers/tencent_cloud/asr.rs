use actix_web::{web, HttpResponse, Responder};
use log::{error, info};
use serde::{Deserialize, Serialize};

use crate::{
    AppState,
    dto::{ApiResponse, error_code::ErrorCode},
    services::{
        tencent_cloud::{CreateRecTaskRequest, TencentAsrServiceError},
        mysql::{MySqlTencentAsrTaskService, MySqlTencentAsrTaskServiceError},
    },
};

/// 创建语音识别任务请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateAsrTaskRequest {
    /// 关联的任务表ID（可选）
    pub related_task_id: Option<u64>,

    /// 语音数据来源，0：语音URL，1：语音数据（post body）
    pub source_type: Option<u32>,

    /// 引擎模型类型，支持：16k_zh、16k_zh_video、16k_en、16k_ca、16k_ja、16k_zh_edu等
    pub engine_model_type: Option<String>,
    /// 音频声道数，仅支持单声道，请填写1
    pub channel_num: Option<u32>,

    /// 自定义热词ID，用于提高特定词汇的识别准确率
    pub customization_id: Option<String>,
    /// 热词ID，用于提高特定词汇的识别准确率
    pub hotword_id: Option<String>,
    /// 音频文件的下载地址
    pub url: String,
    /// 说话人分离人数，取值范围：0-10，0代表自动分离（目前仅支持≤6个人），1-10代表指定说话人数分离
    pub speaker_number: Option<u32>,
    /// 回调地址，用户自行搭建的用于接收识别结果的服务器地址，长度小于2048字节
    pub callback_url: Option<String>,
    /// 识别结果文本格式，0：基础格式，1：详细格式，默认为0
    pub res_text_format: Option<u32>,
}

/// 创建语音识别任务响应DTO
#[derive(Debug, Serialize)]
pub struct CreateAsrTaskResponse {
    /// 任务ID
    pub task_id: u64,
    /// 请求ID
    pub request_id: String,
}

/// 创建语音识别任务
pub async fn create_rec_task(
    app_state: web::Data<AppState>,
    request: web::Json<CreateAsrTaskRequest>,
) -> impl Responder {
    info!("收到创建语音识别任务请求: {:?}", request);

    // 获取腾讯云语音识别服务
    let asr_service = match &app_state.tencent_asr_service {
        Some(service) => service,
        None => {
            error!("腾讯云语音识别服务未初始化");
            return HttpResponse::ServiceUnavailable().json(ApiResponse::error(
                ErrorCode::ServiceUnavailable.code(),
                (),
                "腾讯云语音识别服务不可用",
            ));
        }
    };

    // 构造服务请求
    let service_request = CreateRecTaskRequest {
        source_type: request.source_type.unwrap_or(0),
        engine_model_type: request.engine_model_type.clone().unwrap_or_else(|| "16k_zh_video".to_string()),
        channel_num: request.channel_num.unwrap_or(1),
        customization_id: request.customization_id.clone(),
        hotword_id: request.hotword_id.clone(),
        url: request.url.clone(),
        speaker_number: request.speaker_number.unwrap_or(0),
        callback_url: Some("https://api.xunhewenhua.com/tencent_cloud/asr/callback".to_string()),
        res_text_format: request.res_text_format.unwrap_or(3),
    };

    // 调用腾讯云服务
    match asr_service.create_rec_task(service_request).await {
        Ok(response) => {
            info!("语音识别任务创建成功，任务ID: {}", response.data.task_id);

            // 获取MySQL连接池并保存任务到数据库
            if let Some(db) = &app_state.db {
                if let Some(mysql) = &db.mysql {
                    let mysql_pool = mysql.pool();
                    let task_service = MySqlTencentAsrTaskService::new(mysql_pool.clone());

                    match task_service.create_task_with_related(response.data.task_id, response.request_id.clone(), request.related_task_id).await {
                        Ok(_) => {
                            info!("任务已保存到数据库: task_id={}, related_task_id={:?}", response.data.task_id, request.related_task_id);
                        }
                        Err(e) => {
                            error!("保存任务到数据库失败: {:?}", e);
                            // 不影响主流程，只记录错误
                        }
                    }
                }
            }

            let result = CreateAsrTaskResponse {
                task_id: response.data.task_id,
                request_id: response.request_id,
            };

            HttpResponse::Ok().json(ApiResponse::success(result, "语音识别任务创建成功"))
        }
        Err(e) => {
            let error_message = match &e {
                TencentAsrServiceError::ApiError(msg) => {
                    format!("腾讯云API错误: {}", msg)
                }
                TencentAsrServiceError::HttpError(err) => {
                    format!("HTTP请求错误: {}", err)
                }
                TencentAsrServiceError::JsonError(err) => {
                    format!("JSON解析错误: {}", err)
                }
                TencentAsrServiceError::SignatureError(msg) => {
                    format!("签名计算错误: {}", msg)
                }
                TencentAsrServiceError::ParameterError(msg) => {
                    format!("参数错误: {}", msg)
                }
            };

            error!("创建语音识别任务失败: {}", error_message);

            HttpResponse::InternalServerError().json(ApiResponse::error(
                ErrorCode::SystemError.code(),
                (),
                &error_message,
            ))
        }
    }
}
