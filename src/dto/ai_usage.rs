use serde::{Deserialize, Serialize};

/// 创建AI使用记录请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateAiUsageRequest {
    /// 用户ID
    pub user_id: String,
}

/// 创建AI使用记录响应
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateAiUsageResponse {
    /// 记录ID
    pub id: String,
    /// 使用次数
    pub usage_count: i32,
}

/// 获取AI使用次数响应
#[derive(Debug, Serialize, Deserialize)]
pub struct GetAiUsageResponse {
    /// 记录ID
    pub id: String,
    /// 用户ID
    pub user_id: String,
    /// 使用次数
    pub usage_count: i32,
}

/// 设置AI使用次数请求
#[derive(Debug, Serialize, Deserialize)]
pub struct SetAiUsageRequest {
    /// 用户ID
    pub user_id: String,
    /// 使用次数
    pub usage_count: i32,
}

/// 设置AI使用次数响应
#[derive(Debug, Serialize, Deserialize)]
pub struct SetAiUsageResponse {
    /// 记录ID
    pub id: String,
    /// 用户ID
    pub user_id: String,
    /// 使用次数
    pub usage_count: i32,
}
