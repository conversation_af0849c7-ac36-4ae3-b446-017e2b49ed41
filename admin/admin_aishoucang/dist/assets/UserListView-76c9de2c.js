import{F as Q,G as S,_ as R,r as u,q as W,b as o,H as X,o as M,c as Y,e as i,f as a,w as n,i as Z,I as ee,J as ae,E as d,h as C,j as m,v as b,K as le,x as te,L as ne,M as se,l as oe}from"./index-2a471965.js";const ie=_=>Q("/users/list",_),re=_=>S("/ai-usage/create",{user_id:_}),ue=(_,w)=>S("/ai-usage/set",{user_id:_,usage_count:w});const de={class:"user-management"},ce={class:"operation-bar"},me={class:"left-actions"},_e={class:"right-actions"},pe={class:"table-container"},ge={class:"dialog-footer"},fe={__name:"UserListView",setup(_){const w=u(!1),x=u([]),z=u(0),p=u(1),V=u(50),g=u(""),f=u(!1),U=u(!1),s=u({id:"",nickname:"",phone:"",currentUsage:0,newUsage:0}),c=async()=>{w.value=!0;try{const t={page:p.value,page_size:V.value};g.value&&g.value.trim()&&(t.nickname=g.value.trim());const e=await ie(t);e.code===0?(x.value=e.data.users,z.value=e.data.total):d.error(e.message||"获取用户列表失败")}catch(t){console.error("获取用户列表失败:",t),d.error("获取用户列表失败")}finally{w.value=!1}},L=()=>{p.value=1,c()},B=()=>{g.value="",p.value=1,c()},E=t=>{V.value=t,p.value=1,c()},$=t=>{p.value=t,c()},K=async t=>{try{await te.confirm(`确定要为用户 ${t.nickname||t.phone} 开通会员吗？`,"确认开通",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await re(t.id.toString());e.code===0?(d.success("开通会员成功"),await c()):d.error(e.message||"开通会员失败")}catch(e){e!=="cancel"&&(console.error("开通会员失败:",e),d.error("开通会员失败"))}},N=t=>{s.value={id:t.id.toString(),nickname:t.nickname||"未设置",phone:t.phone,currentUsage:t.remaining_usage||0,newUsage:t.remaining_usage||0},f.value=!0},I=()=>{f.value=!1,s.value={id:"",nickname:"",phone:"",currentUsage:0,newUsage:0}},T=async()=>{try{U.value=!0;const t=await ue(s.value.id,s.value.newUsage);t.code===0?(d.success("设置成功"),f.value=!1,await c()):d.error(t.message||"设置失败")}catch(t){console.error("设置使用次数失败:",t),d.error("设置失败")}finally{U.value=!1}},D=t=>t?new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-";return W(()=>{c()}),(t,e)=>{const k=o("el-icon"),v=o("el-button"),h=o("el-input"),r=o("el-table-column"),j=o("el-avatar"),q=o("el-tag"),F=o("el-table"),G=o("el-pagination"),y=o("el-form-item"),H=o("el-input-number"),J=o("el-form"),P=o("el-dialog"),A=X("loading");return M(),Y("div",de,[e[12]||(e[12]=i("div",{class:"page-header"},[i("div",{class:"breadcrumb"},[i("span",{class:"breadcrumb-item"},"用户管理")])],-1)),i("div",ce,[i("div",me,[a(v,{type:"primary",size:"small"},{default:n(()=>[a(k,null,{default:n(()=>[a(C(ne))]),_:1}),e[7]||(e[7]=m(" 新增用户 "))]),_:1,__:[7]})]),i("div",_e,[a(h,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=l=>g.value=l),placeholder:"请输入昵称搜索",class:"search-input",size:"small",clearable:"",onKeyup:Z(L,["enter"]),onClear:B},{append:n(()=>[a(v,{onClick:L},{default:n(()=>[a(k,null,{default:n(()=>[a(C(se))]),_:1}),e[8]||(e[8]=m(" 搜索 "))]),_:1,__:[8]})]),_:1},8,["modelValue"])])]),i("div",pe,[ee((M(),ae(F,{data:x.value,class:"data-table",stripe:"",border:"","header-cell-style":{background:"#fafafa",color:"#333"}},{default:n(()=>[a(r,{type:"selection",width:"55",align:"center"}),a(r,{prop:"id",label:"用户ID","min-width":"80",align:"center"}),a(r,{prop:"phone",label:"手机号","min-width":"130",align:"center"}),a(r,{prop:"nickname",label:"昵称","min-width":"120"},{default:n(l=>[i("span",null,b(l.row.nickname||"-"),1)]),_:1}),a(r,{label:"头像","min-width":"80",align:"center"},{default:n(l=>[a(j,{size:32,src:l.row.avatar,class:"user-avatar"},{default:n(()=>[a(k,null,{default:n(()=>[a(C(oe))]),_:1})]),_:2},1032,["src"])]),_:1}),a(r,{label:"会员状态","min-width":"100",align:"center"},{default:n(l=>[a(q,{type:l.row.is_member?"success":"info",size:"small"},{default:n(()=>[m(b(l.row.is_member?"会员":"普通用户"),1)]),_:2},1032,["type"])]),_:1}),a(r,{label:"剩余次数","min-width":"100",align:"center"},{default:n(l=>[i("span",{class:le({"low-usage":l.row.remaining_usage<=5&&l.row.is_member})},b(l.row.is_member?l.row.remaining_usage:"-"),3)]),_:1}),a(r,{prop:"created_at",label:"注册时间","min-width":"160",align:"center"},{default:n(l=>[i("span",null,b(D(l.row.created_at)),1)]),_:1}),a(r,{prop:"updated_at",label:"最后更新","min-width":"160",align:"center"},{default:n(l=>[i("span",null,b(D(l.row.updated_at)),1)]),_:1}),a(r,{label:"操作",width:"120",align:"center",fixed:"right"},{default:n(l=>[a(v,{type:"text",size:"small",onClick:O=>K(l.row),disabled:l.row.is_member},{default:n(()=>[m(b(l.row.is_member?"已开通":"开通会员"),1)]),_:2},1032,["onClick","disabled"]),a(v,{type:"text",size:"small",onClick:O=>N(l.row),disabled:!l.row.is_member},{default:n(()=>e[9]||(e[9]=[m(" 编辑会员 ")])),_:2,__:[9]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[A,w.value]])]),a(G,{class:"pagination","current-page":p.value,"page-size":V.value,"page-sizes":[10,20,50,100],total:z.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:E,onCurrentChange:$},null,8,["current-page","page-size","total"]),a(P,{modelValue:f.value,"onUpdate:modelValue":e[6]||(e[6]=l=>f.value=l),title:"编辑会员",width:"400px","before-close":I},{footer:n(()=>[i("span",ge,[a(v,{onClick:e[5]||(e[5]=l=>f.value=!1)},{default:n(()=>e[10]||(e[10]=[m("取消")])),_:1,__:[10]}),a(v,{type:"primary",onClick:T,loading:U.value},{default:n(()=>e[11]||(e[11]=[m("确定")])),_:1,__:[11]},8,["loading"])])]),default:n(()=>[a(J,{model:s.value,"label-width":"100px"},{default:n(()=>[a(y,{label:"用户昵称"},{default:n(()=>[a(h,{modelValue:s.value.nickname,"onUpdate:modelValue":e[1]||(e[1]=l=>s.value.nickname=l),disabled:""},null,8,["modelValue"])]),_:1}),a(y,{label:"手机号"},{default:n(()=>[a(h,{modelValue:s.value.phone,"onUpdate:modelValue":e[2]||(e[2]=l=>s.value.phone=l),disabled:""},null,8,["modelValue"])]),_:1}),a(y,{label:"当前次数"},{default:n(()=>[a(h,{modelValue:s.value.currentUsage,"onUpdate:modelValue":e[3]||(e[3]=l=>s.value.currentUsage=l),disabled:""},null,8,["modelValue"])]),_:1}),a(y,{label:"设置次数",required:""},{default:n(()=>[a(H,{modelValue:s.value.newUsage,"onUpdate:modelValue":e[4]||(e[4]=l=>s.value.newUsage=l),min:0,max:9999,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},be=R(fe,[["__scopeId","data-v-bacc8f03"]]);export{be as default};
